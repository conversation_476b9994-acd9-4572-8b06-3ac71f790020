"use client";

import { ProtectedRoute } from "@/components/auth/protected-route";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import {
  SidebarInset,
  SidebarProvider,
} from "@/components/ui/sidebar";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import { ArrowLeft, HelpCircle } from "lucide-react";
import { ProjectSidebar } from "../../../../../components/project-sidebar";
import { useBusinessItemStore } from "../../../../../stores/businessItemStore";
import { SidebarButton } from "../../../../../components/ui/sidebar-button";
import { ICON_SIZES } from "../../../../../lib/constants";
import { useResizable } from "../../../../../hooks/useResizable";
import { useAnalytics } from "../../../../../hooks/useAnalytics";
import { HowItWorksModal } from "../../../../../components/how-it-works-modal";

// Header component for item detail view
function ItemDetailHeader({
  selectedBusinessItem,
  itemDetails,
  onHelpClick
}: {
  selectedBusinessItem: any;
  itemDetails: any[];
  onHelpClick: () => void;
}) {
  const { trackClick, trackCustomEvent } = useAnalytics();

  // Get the first item detail for the question
  const firstDetail = itemDetails && itemDetails.length > 0 ? itemDetails[0] : null;

  return (
    <header className="flex h-20 shrink-0 items-center gap-2 transition-[width] ease-linear border-b border-border">
      <div className="flex items-center justify-between w-full h-full px-4">
        {/* Left side - Question */}
        <div className="flex items-center gap-4 h-full">
          <h1 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
            {firstDetail?.question || selectedBusinessItem?.description || 'No question available'}
          </h1>
        </div>

        {/* Right side - Help button */}
        <div className="flex items-center gap-2 h-full">
          <SidebarButton
            icon={HelpCircle}
            variant="ghost"
            size="lg"
            layout="icon-only"
            showBorder={true}
            hoverColor="grey"
            hoverScale={true}
            onClick={() => {
              trackClick("help-button", "item-detail-header");
              trackCustomEvent("help_clicked", {
                from_item: selectedBusinessItem?.title,
                location: "header"
              });
              onHelpClick();
            }}
            iconClassName={ICON_SIZES.lg}
          />
        </div>
      </div>
    </header>
  );
}

export default function ItemDetailPage() {
  const params = useParams();
  const router = useRouter();
  const projectId = params.id;
  const itemId = params.itemId;
  
  const [chatWidth, setChatWidth] = useState<'45%' | '45%'>('45%');
  const [isChatCollapsed, setIsChatCollapsed] = useState(false);
  const [isHowItWorksOpen, setIsHowItWorksOpen] = useState(false);

  const { selectedItem, itemDetails, setSelectedItem, setItemDetails } = useBusinessItemStore();

  // Load item details on mount
  useEffect(() => {
    const loadItemDetails = async () => {
      try {
        const { fetchItemDetails, fetchItemById } = await import('@/stores/businessItemStore');
        
        // If we don't have the selected item, fetch it by ID
        if (!selectedItem && itemId) {
          const item = await fetchItemById(itemId as string);
          setSelectedItem(item);
        }
        
        // Fetch item details
        if (itemId) {
          const details = await fetchItemDetails(itemId as string);
          setItemDetails(details);
        }
      } catch (err) {
        console.error('Error loading item details:', err);
        setItemDetails([]);
      }
    };

    loadItemDetails();
  }, [itemId, selectedItem, setSelectedItem, setItemDetails]);

  // Handle back to project
  const handleBackToProject = () => {
    router.push(`/projects/${projectId}`);
  };

  // Handle chat width change
  const handleChatWidthChange = (width: '45%' | '45%') => {
    setChatWidth(width);
  };

  // Resize functionality for sidebar
  const resizable = useResizable({
    initialWidth: '30vw',
    minWidthPercent: 20,
    maxWidthPercent: 50,
    onWidthChange: (width) => {
      // Handle resize if needed
    },
    onExpand: () => {
      setIsChatCollapsed(false);
    },
    onCollapse: () => {
      setIsChatCollapsed(true);
    },
  });

  return (
    <ProtectedRoute>
      <div className="h-screen overflow-hidden">
        <SidebarProvider
          defaultOpen={true}
          style={{
            '--sidebar-width': '30vw',
            '--sidebar-width-mobile': '18rem',
            '--sidebar-width-icon': '5rem',
          } as React.CSSProperties}
        >
          <ProjectSidebar
            projectId={projectId as string}
            chatWidth={chatWidth}
            setChatWidth={handleChatWidthChange}
            isChatCollapsed={isChatCollapsed}
            setIsChatCollapsed={setIsChatCollapsed}
            selectedBusinessItem={selectedItem}
            showDescription={true}
            onBackToProject={handleBackToProject}
            resizeHandle={{
              onMouseDown: resizable.handleMouseDown,
              isDragging: resizable.isDragging,
            }}
          />
          <SidebarInset className="flex-1 flex flex-col h-screen overflow-hidden">
            <ItemDetailHeader
              selectedBusinessItem={selectedItem}
              itemDetails={itemDetails}
              onHelpClick={() => setIsHowItWorksOpen(true)}
            />

            {/* Main Content Area */}
            <div className="flex-1 min-w-0 min-h-0 w-full max-w-full p-4 pb-20 overflow-y-auto bg-gray-50 dark:bg-gray-900 relative project-main-content">
              {/* Answer Box */}
              {selectedItem && itemDetails && itemDetails.length > 0 && (
                <div className="max-w-4xl mx-auto">
                  <div className="bg-green-50 border-2 border-green-200 rounded-lg p-6 shadow-sm">
                    <div className="text-green-800 text-lg leading-relaxed">
                      {itemDetails[0]?.answer || 'No answer available'}
                    </div>
                  </div>
                </div>
              )}

              {/* Loading state */}
              {!selectedItem && (
                <div className="flex items-center justify-center h-64">
                  <div className="text-center">
                    <div className={`animate-spin rounded-full ${ICON_SIZES.lg} border-b-2 border-gray-900 mx-auto mb-4`}></div>
                    <p className="text-gray-600">Loading item details...</p>
                  </div>
                </div>
              )}

              {/* Loading item details state */}
              {selectedItem && (!itemDetails || itemDetails.length === 0) && (
                <div className="flex items-center justify-center h-64">
                  <div className="text-center">
                    <div className={`animate-spin rounded-full ${ICON_SIZES.lg} border-b-2 border-gray-900 mx-auto mb-4`}></div>
                    <p className="text-gray-600">Loading answer...</p>
                  </div>
                </div>
              )}
            </div>

            {/* Bottom fade effect */}
            <div
              className="fixed bottom-0 h-16 bg-gradient-to-t from-gray-50 via-gray-50/80 to-transparent dark:from-gray-900 dark:via-gray-900/80 dark:to-transparent pointer-events-none z-10"
              style={{
                left: '30vw',
                right: '0'
              }}
            />
            <div
              className="fixed bottom-0 h-8 backdrop-blur-sm pointer-events-none z-10"
              style={{
                left: '30vw',
                right: '0'
              }}
            />
          </SidebarInset>
        </SidebarProvider>

        {/* How It Works Modal */}
        <HowItWorksModal
          isOpen={isHowItWorksOpen}
          onClose={() => setIsHowItWorksOpen(false)}
        />
      </div>
    </ProtectedRoute>
  );
}
