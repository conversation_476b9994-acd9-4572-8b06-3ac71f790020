"use client";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, Lightbulb, Zap, Target } from "lucide-react";

interface HowItWorksModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function HowItWorksModal({ isOpen, onClose }: HowItWorksModalProps) {
  const tableFeatures = [
    {
      icon: Lightbulb,
      title: "Title Column",
      description: "Shows the main title/name of each business item or task",
      badge: "Column 1",
      color: "bg-blue-100 text-blue-700 border-blue-300"
    },
    {
      icon: Zap,
      title: "Actions Column",
      description: "Describes what actions have been taken or need to be taken for this item",
      badge: "Column 2",
      color: "bg-yellow-100 text-yellow-700 border-yellow-300"
    },
    {
      icon: Target,
      title: "Results Column",
      description: "Shows the outcomes, results, or current status of the implemented actions",
      badge: "Column 3",
      color: "bg-orange-100 text-orange-700 border-orange-300"
    },
    {
      icon: CheckCircle,
      title: "Status Indicators",
      description: "Visual indicators showing the current state: Idea, Action, Confirmed, or Unproven",
      badge: "Status",
      color: "bg-green-100 text-green-700 border-green-300"
    }
  ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold text-center">
            How the Table Works
          </DialogTitle>
          <DialogDescription className="text-center text-lg">
            Understanding the columns and features of your business item details table
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 mt-6 px-6">
          {tableFeatures.map((feature, index) => {
            const IconComponent = feature.icon;
            return (
              <div key={index} className="flex items-start gap-4 p-4 rounded-lg border border-gray-200 hover:shadow-md transition-shadow">
                <div className={`flex items-center justify-center w-12 h-12 rounded-full ${feature.color} border-2`}>
                  <IconComponent className="w-6 h-6" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h3 className="text-lg font-semibold text-gray-900">{feature.title}</h3>
                    <Badge variant="outline" className="text-xs">
                      {feature.badge}
                    </Badge>
                  </div>
                  <p className="text-gray-600">{feature.description}</p>
                </div>
              </div>
            );
          })}
        </div>

        <div className="mt-8 mx-6 p-4 bg-gray-50 rounded-lg">
          <h4 className="font-semibold text-gray-900 mb-2">Table Features:</h4>
          <ul className="space-y-1 text-sm text-gray-600">
            <li>• Click on cells to edit content inline</li>
            <li>• Drag and drop rows to reorder items</li>
            <li>• Status badges show current progress state</li>
            <li>• Add new rows with the + button</li>
            <li>• Use dropdown menus for additional actions</li>
          </ul>
        </div>

        <div className="flex justify-center mt-6 pb-6">
          <Button onClick={onClose} className="px-8">
            Got it!
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
