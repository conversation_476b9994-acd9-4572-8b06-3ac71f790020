"use client";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, Lightbulb, Zap, Target } from "lucide-react";

interface HowItWorksModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function HowItWorksModal({ isOpen, onClose }: HowItWorksModalProps) {
  const steps = [
    {
      icon: Lightbulb,
      title: "Ideas",
      description: "Start with creative ideas and concepts for your project",
      badge: "Step 1",
      color: "bg-blue-100 text-blue-700 border-blue-300"
    },
    {
      icon: Zap,
      title: "Actions",
      description: "Transform ideas into actionable tasks and implementation plans",
      badge: "Step 2", 
      color: "bg-yellow-100 text-yellow-700 border-yellow-300"
    },
    {
      icon: Target,
      title: "Execution",
      description: "Execute your plans and track progress towards your goals",
      badge: "Step 3",
      color: "bg-orange-100 text-orange-700 border-orange-300"
    },
    {
      icon: CheckCircle,
      title: "Results",
      description: "Measure outcomes and iterate for continuous improvement",
      badge: "Step 4",
      color: "bg-green-100 text-green-700 border-green-300"
    }
  ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold text-center">
            How It Works
          </DialogTitle>
          <DialogDescription className="text-center text-lg">
            Transform your ideas into actionable results with our structured approach
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-6 mt-6">
          {steps.map((step, index) => {
            const IconComponent = step.icon;
            return (
              <div key={index} className="flex items-start gap-4 p-4 rounded-lg border border-gray-200 hover:shadow-md transition-shadow">
                <div className={`flex items-center justify-center w-12 h-12 rounded-full ${step.color} border-2`}>
                  <IconComponent className="w-6 h-6" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h3 className="text-lg font-semibold text-gray-900">{step.title}</h3>
                    <Badge variant="outline" className="text-xs">
                      {step.badge}
                    </Badge>
                  </div>
                  <p className="text-gray-600">{step.description}</p>
                </div>
              </div>
            );
          })}
        </div>

        <div className="mt-8 p-4 bg-gray-50 rounded-lg">
          <h4 className="font-semibold text-gray-900 mb-2">Key Features:</h4>
          <ul className="space-y-1 text-sm text-gray-600">
            <li>• Drag and drop to organize and prioritize items</li>
            <li>• Real-time collaboration with team members</li>
            <li>• Track progress with detailed analytics</li>
            <li>• Export results and share insights</li>
          </ul>
        </div>

        <div className="flex justify-center mt-6">
          <Button onClick={onClose} className="px-8">
            Got it!
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
